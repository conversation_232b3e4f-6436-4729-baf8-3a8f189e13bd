.top-drawer {
  height: 0px;
  position: absolute;
  z-index: 2;
  width: 100%;
  background: linear-gradient(
    0deg,
    var(--background-gradient-start) 11%,
    color-mix(in srgb, var(--background-gradient-mid) 75%, transparent) 50%,
    color-mix(in srgb, var(--background-gradient-end) 100%, transparent) 100%
  );
  border-bottom: 1px solid var(--divi-purp);
  border-bottom-left-radius: 33%;
  border-bottom-right-radius: 33%;

  .message-prefix-icon {
    position: absolute;
    z-index: 9;
    font-size: 27px;
    background: black;
    border-radius: 50%;
    height: 50px;
    width: 50px;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 2px solid #ffa5008c;
    right: -24px;
    top: -10px;
  }

  .toggle-drawer-button {
    margin: auto;
    display: block;
    border-top-left-radius: 0;
    border-top-right-radius: 0;
    width: 75px;
    border: 1px solid hsl(221, 14%, 29%);
    border-top: 0;
    position: absolute;
    left: 47%;
    z-index: 2;
    height: 16px;
  }

  .top-drawer-content {
    width: 70%;
    margin: auto;
    display: flex;
    justify-content: center;

    .chat-card {
      border-width: 2px;
    }

    .chat-message-content {
      position: relative;
      width: 80%;
      overflow: visible;
    }

    .card-content {
      display: flex;
      padding: 11px;
    }

    .save-prefix-btn {
      height: fit-content;
      margin: auto 0 1% 2%;
    }
  }

  .prefix-textarea {
    /* --bulma-input-placeholder-color: #ffffffd4; */
  }

  .save-prefix-banner {
    position: absolute;
    z-index: 1;
    width: 70%;
  }
}

@media (max-width: 768px) {
  .top-drawer .top-drawer-content {
    width: inherit;
  }

  .top-drawer {
    padding: 10px 10px;
  }
}
